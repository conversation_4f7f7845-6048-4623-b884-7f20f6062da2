
---
title: "Robustness Check"
output: html_notebook
---

# Package Installation and Setup
```{r setup, include=FALSE}
# Install required packages if not already installed
if (!require(lme4)) install.packages("lme4")
if (!require(sjPlot)) install.packages("sjPlot")
if (!require(clubSandwich)) install.packages("clubSandwich")
if (!require(modelsummary)) install.packages("modelsummary")
if (!require(gt)) install.packages("gt")
if (!require(flextable)) install.packages("flextable")
if (!require(tibble)) install.packages("tibble")

library(lme4)
library(sjPlot)
library(clubSandwich)
library(modelsummary)
library(gt)
library(flextable)
library(plm)
library(tibble)
```

```{r load_functions}
# Load the Organization & Environment style function
source("modelsummary_OE_style.R")
```

# Data loading
```{r Data analysis}

# Load the data file
load("dta1_20240903.RData")
# You can add a quick check to confirm the data loaded correctly

# 重新计算Age变量，使其随年份变化
library(lubridate)
library(dplyr)
dta1 <- dta1 %>%
  mutate(
    EndYear = ymd(EndYear),
    Year = year(EndYear),
    Age = Year - EstablishYear
  )

```



# Robustness test

## Short-term Impact of Inspection
Inspected 后三年标记为1, inspection当年标记为0
We assign a code of '1' to firms within the three-year period following their initial inspection to assess the short-term effects of regulatory pressure.
Table B2. Short-term Impact of Inspection  

```{r}
# # 创建一个新的数据框，只包含所需的列
# new_df <- select(dta1, Symbol, EndYear, inspection_year, first_inspection, after_first_inspection, extended_inspection)

# na_rows <- dta1 %>% filter(is.na(first_inspection))
# new_df <- select(na_rows, Symbol, EndYear, inspection_year, first_inspection, after_first_inspection, extended_inspection)

```

### P3 connection (continuous)
```{r}
# 加载必要的库
library(lubridate)
library(dplyr)
library(tidyr)

dta3 = dta1

# 创建新变量 extended_inspection
dta3 <- dta3 %>%
  group_by(Symbol) %>%  # 按照单位分组
  mutate(
    # 找到每个单位 first_inspection 为1的年份
    inspection_year = ifelse(first_inspection == 1, Year, NA),
    # 使用 coalesce 处理 NA 值，确保每个单位都有一个 inspection_year
    inspection_year = coalesce(inspection_year, lag(inspection_year, default = NA))
  ) %>%
  # 使用 fill 函数确保 inspection_year 被赋值到所有行
  fill(inspection_year, .direction = "downup") %>%
  ungroup()  # 解除分组

# 标记 inspection_year 及之后的两年
dta3$extended_inspection <- as.numeric(dta3$Year > dta3$inspection_year & dta3$Year <= (dta3$inspection_year + 3))
dta3 <- dta3 %>% mutate(extended_inspection = ifelse(is.na(extended_inspection), 0, extended_inspection))
#P3: The effects of political connection (continous) and policy pressure on environmental information disclosure

library(lme4)
p3ext1 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta3)


p3ext2 <- lmer(Environmental_Information_Disclosure ~ Age + extended_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta3)

p3ext3 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + extended_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + extended_inspection:connection_num + (1|PROVINCE/CITY), data=dta3)

# Create Organization & Environment style table with detailed mixed model statistics
library(sjPlot)

# Define custom CSS for Organization & Environment journal style
oe_css <- list(
  css.table = "border-collapse: collapse; border: none; font-family: Times, serif; font-size: 12px; margin-top: 10px;",
  css.thead = "border-top: 2px solid black; border-bottom: 1px solid black; font-weight: normal; padding: 4px 8px;",
  css.tdata = "border: none; padding: 3px 8px; text-align: center;",
  css.arc = "border-top: 1px solid black;",
  css.caption = "font-weight: bold; text-align: left; padding-bottom: 15px; margin-bottom: 5px;",
  css.subtitle = "font-style: italic; text-align: center; padding-bottom: 8px; margin-bottom: 8px;",
  css.firsttablecol = "text-align: left; padding-left: 0px; border: none;",
  css.leftalign = "text-align: left;",
  css.centeralign = "text-align: center;",
  css.footnote = "font-size: 10px; font-style: italic; border-top: 1px solid black; padding-top: 5px;",
  css.depvarheader = "text-align: center; font-weight: normal; padding: 8px; border-bottom: 1px solid #ccc; background-color: #f9f9f9;"
)

tab_model(p3ext1, p3ext2, p3ext3,
          title = "Table B2. Short-term Impact of Inspection",
          dv.labels = "Dependent variable:<br>Environmental Information Disclosure",
          show.aic = TRUE,
          show.loglik = TRUE,
          show.obs = TRUE,
          show.ngroups = TRUE,
          show.re.var = TRUE,
          show.icc = TRUE,
          show.intercept = FALSE, # Hide intercept
          show.p = FALSE,        # Hide p-values column
          show.se = TRUE,        # Show standard errors in parentheses
          show.ci = FALSE,       # Hide confidence intervals
          p.style = "stars",     # Display significance as stars
          p.threshold = c(0.1, 0.05, 0.01),  # Set significance thresholds
          digits = 3,            # 3 decimal places
          digits.re = 2,         # 2 decimal places for random effects
          pred.labels = c("Age", "Connections", "Inspection influence", "ESG Rating",
                         "Log(Registered Capital)", "Return on Assets", "Leverage",
                         "Inspection influence × Connections"),
          CSS = oe_css,
          file = "B2_p3_extended_inspection_OE_style.html")

# Fix table header alignment
fix_table_header <- function(filename) {
  if (file.exists(filename)) {
    content <- readLines(filename, warn = FALSE)
    # Fix colspan for dependent variable header
    content <- gsub('colspan="2"([^>]*>Dependent variable)', 'colspan="6"\\1', content)
    # Ensure center alignment for the header
    content <- gsub('(colspan="6"[^>]*style="[^"]*)(">Dependent variable)', '\\1 text-align: center;\\2', content)
    writeLines(content, filename)
  }
}

fix_table_header("B2_p3_extended_inspection_OE_style.html")


```

### P4 contral/local connection (continuous)
Table B3. Short-term Impact of Inspection (central and local connections)
```{r}

library(lme4)

p4ext1 <- lmer(Environmental_Information_Disclosure ~ Age + central_connection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta3)

p4ext2 <- lmer(Environmental_Information_Disclosure ~ Age + central_connection * extended_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta3)

p4ext3 <- lmer(Environmental_Information_Disclosure ~ Age + local_connection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta3)

p4ext4 <- lmer(Environmental_Information_Disclosure ~ Age + local_connection * extended_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta3)

# Create Organization & Environment style table for central/local connections
tab_model(p4ext1, p4ext2, p4ext3, p4ext4,
          title = "Table B3. Short-term Impact of Inspection (Central and Local Connections)",
          dv.labels = "Dependent variable:<br>Environmental Information Disclosure",
          rm.terms = c("as.factor(IndustryName)"),
          show.aic = TRUE,
          show.loglik = TRUE,
          show.obs = TRUE,
          show.ngroups = TRUE,
          show.re.var = TRUE,
          show.icc = TRUE,
          show.intercept = FALSE, # Hide intercept
          show.p = FALSE,        # Hide p-values column
          show.se = TRUE,        # Show standard errors in parentheses
          show.ci = FALSE,       # Hide confidence intervals
          p.style = "stars",     # Display significance as stars
          p.threshold = c(0.1, 0.05, 0.01),  # Set significance thresholds
          digits = 3,            # 3 decimal places
          digits.re = 2,         # 2 decimal places for random effects
          CSS = oe_css,
          file = "B3_p4_extended_inspection_OE_style.html")
```



## State-owned Enterprises
### P3 SOE new dataset
Table B4. State-owned Enterprises
We labelled firms where the actual controllers are state-owned institutions or state-owned enterprises as politically connected. 
```{r P3 SOE new dataset 1}
#P3

library(lme4)
p1soe1 <- lmer(Environmental_Information_Disclosure ~ Age + SOE_new + ROA + ESG_Rate + Leverage + RegisterCapital_log + (1|PROVINCE/CITY), data=dta1)

p1soe2 <- lmer(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p1soe3 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * SOE_new + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

# Create Organization & Environment style table for SOE analysis
tab_model(p1soe1, p1soe2, p1soe3,
          title = "Table B4. State-owned Enterprises and Environmental Disclosure",
          dv.labels = "Dependent variable:<br>Environmental Information Disclosure",
          rm.terms = c("as.factor(IndustryName)"),
          show.aic = TRUE,
          show.loglik = TRUE,
          show.obs = TRUE,
          show.ngroups = TRUE,
          show.re.var = TRUE,
          show.icc = TRUE,
          show.intercept = FALSE, # Hide intercept
          show.p = FALSE,        # Hide p-values column
          show.se = TRUE,        # Show standard errors in parentheses
          show.ci = FALSE,       # Hide confidence intervals
          p.style = "stars",     # Display significance as stars
          p.threshold = c(0.1, 0.05, 0.01),  # Set significance thresholds
          digits = 3,            # 3 decimal places
          digits.re = 2,         # 2 decimal places for random effects
          CSS = oe_css,
          file = "B4_SOE_result_p1_OE_style.html")

```


### P4 central/local SOE Level
```{r P4 central/local SEO level 0}
library(lme4)
p2soe1 <- lmer(Environmental_Information_Disclosure ~ SOE_new_central + Age + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2soe2 <- lmer(Environmental_Information_Disclosure ~ SOE_new_central * after_first_inspection + Age + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2soe3 <- lmer(Environmental_Information_Disclosure ~ SOE_new_local + Age + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2soe4 <- lmer(Environmental_Information_Disclosure ~ Age + SOE_new_local * after_first_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

# Create Organization & Environment style table for central/local SOE analysis
tab_model(p2soe1, p2soe2, p2soe3, p2soe4,
          title = "Table B5. Central vs Local State-owned Enterprises",
          dv.labels = "Dependent variable:<br>Environmental Information Disclosure",
          rm.terms = c("as.factor(IndustryName)"),
          show.aic = TRUE,
          show.loglik = TRUE,
          show.obs = TRUE,
          show.ngroups = TRUE,
          show.re.var = TRUE,
          show.icc = TRUE,
          show.intercept = FALSE, # Hide intercept
          show.p = FALSE,        # Hide p-values column
          show.se = TRUE,        # Show standard errors in parentheses
          show.ci = FALSE,       # Hide confidence intervals
          p.style = "stars",     # Display significance as stars
          p.threshold = c(0.1, 0.05, 0.01),  # Set significance thresholds
          digits = 3,            # 3 decimal places
          digits.re = 2,         # 2 decimal places for random effects
          CSS = oe_css,
          file = "B5_SOE_result_p2_OE_style.html")
```

## Plm model
### P3 connection (continuous)
```{r P3 connection (continuous)}
#P3: The effects of political connection (continous) and policy pressure on environmental information disclosure
library(plm)
p3plm1 <- plm(Environmental_Information_Disclosure ~ connection_num + ROA + ESG_Rate + Leverage + as.factor(IndustryName)+ as.factor(PROVINCE) + RegisterCapital_log + ESG_Rate, data=dta1,
              index=c("Symbol", "EndYear"),model="within",effect="twoways")


p3plm2 <- plm(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate  + as.factor(IndustryName)+ as.factor(PROVINCE) + RegisterCapital_log + ROA + Leverage +ESG_Rate, data=dta1,
              index=c("Symbol"),model="within")

p3plm3 <- plm(Environmental_Information_Disclosure ~ after_first_inspection * connection_num + ESG_Rate  + as.factor(IndustryName)+ as.factor(PROVINCE) + RegisterCapital_log + ROA + Leverage + ESG_Rate, data=dta1,
              index=c("Symbol"),model="within")

# Create Organization & Environment style table for PLM models using modelsummary
library(modelsummary)

# Define coefficient names for cleaner display
coef_map_plm <- c(
  "Age" = "Age",
  "connection_num" = "Connections",
  "after_first_inspection" = "After First Inspection",
  "ESG_Rate" = "ESG Rating",
  "ROA" = "Return on Assets",
  "Leverage" = "Leverage",
  "RegisterCapital_log" = "Log(Registered Capital)",
  "connection_num:after_first_inspection" = "Connections × After Inspection"
)

# Create rows for fixed effects that will appear before statistics
fe_rows <- tribble(
  ~term, ~`Two-way FE`, ~`Individual FE`, ~`Interaction Model`,
  "Firm fixed effects", "Y", "Y", "Y",
  "Industry fixed effects", "Y", "Y", "Y",
  "Province fixed effects", "Y", "Y", "Y",
  "Year fixed effects", "Y", "N", "N"
)

# Define custom goodness-of-fit map
gof_map_plm <- tribble(
  ~raw, ~clean, ~fmt,
  "nobs", "Num.Obs.", 0,
  "r.squared", "R²", 3,
  "adj.r.squared", "Adj.R²", 3
)

# Create modelsummary table with Organization & Environment style
plm_p3_table <- modelsummary(
  list("Two-way FE" = p3plm1, "Individual FE" = p3plm2, "Interaction Model" = p3plm3),
  output = "gt",
  stars = c('*' = .1, '**' = .05, '***' = .01),
  coef_map = coef_map_plm,
  gof_map = gof_map_plm,
  title = "Table B6. Fixed Effects Models: Political Connections",
  notes = list("Standard errors in parentheses.",
              "* p < 0.1, ** p < 0.05, *** p < 0.01"),
  fmt = 3,
  estimate = "{estimate}{stars}",
  statistic = "({std.error})",
  add_rows = fe_rows
) %>%
  tab_style(
    style = cell_text(weight = "bold"),
    locations = cells_column_labels()
  ) %>%
  tab_style(
    style = cell_text(weight = "bold"),
    locations = cells_stub()
  ) %>%
  tab_options(
    table.font.size = 11,
    heading.title.font.size = 12,
    heading.subtitle.font.size = 11,
    table.border.top.style = "solid",
    table.border.bottom.style = "solid",
    heading.border.bottom.style = "solid",
    column_labels.border.top.style = "solid",
    column_labels.border.bottom.style = "solid",
    stub.border.style = "solid"
  )

# Save to file
gtsave(plm_p3_table, "B6_plm_p3_OE_style.html")
print(plm_p3_table)
```

### P4 contral/local connection (continuous)
```{r P4 contral/local connection (continuous)}
#P4: The effects of central and local political connection (continuous) and policy pressure on environmental information disclosure
library(plm)

p4plm1 <- plm(Environmental_Information_Disclosure ~ central_connection + ESG_Rate  + RegisterCapital_log + as.factor(IndustryName)+ as.factor(PROVINCE) + ROA + Leverage +ESG_Rate, data=dta1,
            index=c("Symbol", "EndYear"),model="within",effect="twoways")

p4plm2 <- plm(Environmental_Information_Disclosure ~ central_connection * after_first_inspection + ESG_Rate  + as.factor(IndustryName)+ as.factor(PROVINCE) + RegisterCapital_log + ROA + Leverage +ESG_Rate, data=dta1,
            index=c("Symbol"),model="within")

p4plm3 <- plm(Environmental_Information_Disclosure ~ local_connection + ESG_Rate  + RegisterCapital_log + as.factor(IndustryName)+ as.factor(PROVINCE) + ROA + Leverage +ESG_Rate, data=dta1,
            index=c("Symbol", "EndYear"),model="within",effect="twoways")

p4plm4 <- plm(Environmental_Information_Disclosure ~ local_connection * after_first_inspection + ESG_Rate  + as.factor(IndustryName)+ as.factor(PROVINCE) + RegisterCapital_log + ROA + Leverage +ESG_Rate, data=dta1,
            index=c("Symbol"),model="within")

# Create Organization & Environment style table for PLM central/local models using modelsummary
# Define coefficient names for central/local connections
coef_map_plm_p4 <- c(
  "Age" = "Age",
  "central_connection" = "Central Connections",
  "local_connection" = "Local Connections",
  "after_first_inspection" = "After First Inspection",
  "ESG_Rate" = "ESG Rating",
  "ROA" = "Return on Assets",
  "Leverage" = "Leverage",
  "RegisterCapital_log" = "Log(Registered Capital)",
  "central_connection:after_first_inspection" = "Central Connections × After Inspection",
  "local_connection:after_first_inspection" = "Local Connections × After Inspection"
)

# Create additional rows for fixed effects information for P4 models
fe_rows_p4 <- tribble(
  ~term, ~`Central (Two-way)`, ~`Central × Inspection`, ~`Local (Two-way)`, ~`Local × Inspection`,
  "Firm fixed effects", "Y", "Y", "Y", "Y",
  "Industry fixed effects", "Y", "Y", "Y", "Y",
  "Province fixed effects", "Y", "Y", "Y", "Y",
  "Year fixed effects", "Y", "N", "Y", "N"
)

# Create modelsummary table with Organization & Environment style
plm_p4_table <- modelsummary(
  list("Central (Two-way)" = p4plm1, "Central × Inspection" = p4plm2,
       "Local (Two-way)" = p4plm3, "Local × Inspection" = p4plm4),
  output = "gt",
  stars = c('*' = .1, '**' = .05, '***' = .01),
  coef_map = coef_map_plm_p4,
  gof_map = gof_map_plm,
  title = "Table B7. Fixed Effects Models: Central vs Local Political Connections",
  notes = list("Standard errors in parentheses.",
              "* p < 0.1, ** p < 0.05, *** p < 0.01"),
  fmt = 3,
  estimate = "{estimate}{stars}",
  statistic = "({std.error})",
  add_rows = fe_rows_p4
) %>%
  tab_style(
    style = cell_text(weight = "bold"),
    locations = cells_column_labels()
  ) %>%
  tab_style(
    style = cell_text(weight = "bold"),
    locations = cells_stub()
  ) %>%
  tab_options(
    table.font.size = 11,
    heading.title.font.size = 12,
    heading.subtitle.font.size = 11,
    table.border.top.style = "solid",
    table.border.bottom.style = "solid",
    heading.border.bottom.style = "solid",
    column_labels.border.top.style = "solid",
    column_labels.border.bottom.style = "solid",
    stub.border.style = "solid"
  )

# Save to file
gtsave(plm_p4_table, "B7_plm_p4_OE_style.html")
print(plm_p4_table)
```
